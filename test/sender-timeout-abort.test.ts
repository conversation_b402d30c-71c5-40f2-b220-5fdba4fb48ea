import { createServer, type Server } from 'node:http'
import { promisify } from 'node:util'
import { Sender } from '../app/utils/sender/sender'

interface TestResult {
    name: string
    passed: boolean
    error?: string
    duration?: number
}

class TestRunner {
    private readonly results: TestResult[] = []
    private server: Server | null = null
    private serverPort = 0

    async setupTestServer(): Promise<void> {
        return new Promise((resolve) => {
            this.server = createServer((req, res) => {
                const url = req.url || ''

                if (url.includes('/slow')) {
                    // Slow endpoint - delays 2 seconds before responding
                    setTimeout(() => {
                        res.writeHead(200, { 'Content-Type': 'application/json' })
                        res.end(JSON.stringify({ message: 'slow response', timestamp: Date.now() }))
                    }, 2000)
                } else if (url.includes('/fast')) {
                    // Fast endpoint - responds immediately
                    res.writeHead(200, { 'Content-Type': 'application/json' })
                    res.end(JSON.stringify({ message: 'fast response', timestamp: Date.now() }))
                } else {
                    // Default endpoint
                    res.writeHead(200, { 'Content-Type': 'application/json' })
                    res.end(JSON.stringify({ message: 'default response', timestamp: Date.now() }))
                }
            })

            this.server.listen(0, () => {
                const address = this.server!.address()

                if (address && typeof address === 'object') {
                    this.serverPort = address.port
                }

                resolve()
            })
        })
    }

    async teardownTestServer(): Promise<void> {
        if (this.server) {
            await promisify(this.server.close.bind(this.server))()
            this.server = null
        }
    }

    async runTest(name: string, testFn: () => Promise<void>): Promise<void> {
        const startTime = Date.now()

        try {
            await testFn()

            this.results.push({
                name,
                passed: true,
                duration: Date.now() - startTime,
            })

            console.log(`✅ PASS: ${name} (${Date.now() - startTime}ms)`)
        } catch (error) {
            this.results.push({
                name,
                passed: false,
                error: error instanceof Error ? error.message : String(error),
                duration: Date.now() - startTime,
            })

            console.log(`❌ FAIL: ${name} (${Date.now() - startTime}ms)`)
            console.log(`   Error: ${error instanceof Error ? error.message : String(error)}`)
        }
    }

    async testTimeoutFunctionality(): Promise<void> {
        await this.runTest('Timeout functionality - request should be aborted after timeout', async () => {
            const sender = new Sender(`http://localhost:${this.serverPort}/slow`, {
                timeout: 1000, // 1 second timeout
            })

            const startTime = Date.now()

            try {
                await sender.send('{"test": "timeout"}')
                throw new Error('Request should have been aborted due to timeout')
            } catch (error) {
                const duration = Date.now() - startTime

                console.log(`   📊 Request duration: ${duration}ms`)
                console.log(`   📊 Error type: ${error instanceof Error ? error.constructor.name : typeof error}`)
                console.log(`   📊 Error message: ${error instanceof Error ? error.message : String(error)}`)

                // Check if request was aborted due to timeout (should be around 1000ms, not 2000ms)
                if (duration > 1500) {
                    throw new Error(`Request took too long (${duration}ms), timeout may not be working. Expected ~1000ms but got ${duration}ms. This suggests retry mechanism is interfering with timeout.`)
                }

                // Check if error is related to timeout/abort
                const errorMessage = error instanceof Error ? error.message.toLowerCase() : String(error).toLowerCase()

                if (!errorMessage.includes('abort') && !errorMessage.includes('timeout') && !errorMessage.includes('cancelled')) {
                    throw new Error(`Unexpected error type: ${errorMessage}`)
                }

                console.log(`   ✓ Request correctly aborted after ${duration}ms`)
            }
        })

        // Test with retry disabled to confirm the issue
        await this.runTest('Timeout functionality with retry disabled - should work correctly', async () => {
            const sender = new Sender(`http://localhost:${this.serverPort}/slow`, {
                timeout: 1000, // 1 second timeout
                retry: false, // Disable retry
            })

            const startTime = Date.now()

            try {
                await sender.send('{"test": "timeout-no-retry"}')
                throw new Error('Request should have been aborted due to timeout')
            } catch (error) {
                const duration = Date.now() - startTime

                console.log(`   📊 Request duration (no retry): ${duration}ms`)
                console.log(`   📊 Error type: ${error instanceof Error ? error.constructor.name : typeof error}`)
                console.log(`   📊 Error message: ${error instanceof Error ? error.message : String(error)}`)

                // With retry disabled, timeout should work correctly
                if (duration > 1500) {
                    throw new Error(`Request took too long (${duration}ms) even with retry disabled`)
                }

                console.log(`   ✓ Request correctly aborted after ${duration}ms (retry disabled)`)
            }
        })
    }

    async testAbortSignalFunctionality(): Promise<void> {
        await this.runTest('AbortSignal functionality - request should be aborted when signal is triggered', async () => {
            const sender = new Sender(`http://localhost:${this.serverPort}/slow`, {
                timeout: 5000, // Long timeout to ensure abort signal triggers first
            })

            const abortController = new AbortController()
            const startTime = Date.now()

            // Abort the request after 500ms
            setTimeout(() => {
                console.log(`   📊 Triggering abort signal at ${Date.now() - startTime}ms`)
                abortController.abort()
            }, 500)

            try {
                await sender.send('{"test": "abort"}', {
                    signal: abortController.signal,
                })

                throw new Error('Request should have been aborted due to abort signal')
            } catch (error) {
                const duration = Date.now() - startTime

                console.log(`   📊 Request duration: ${duration}ms`)
                console.log(`   📊 Error type: ${error instanceof Error ? error.constructor.name : typeof error}`)
                console.log(`   📊 Error message: ${error instanceof Error ? error.message : String(error)}`)

                // Check if request was aborted quickly (should be around 500ms, not 2000ms)
                if (duration > 1000) {
                    throw new Error(`Request took too long (${duration}ms), abort signal may not be working. Expected ~500ms but got ${duration}ms. This suggests retry mechanism is interfering with abort signal.`)
                }

                // Check if error is related to abort
                const errorMessage = error instanceof Error ? error.message.toLowerCase() : String(error).toLowerCase()

                if (!errorMessage.includes('abort') && !errorMessage.includes('cancelled')) {
                    throw new Error(`Unexpected error type: ${errorMessage}`)
                }

                console.log(`   ✓ Request correctly aborted after ${duration}ms`)
            }
        })

        // Test with retry disabled to confirm the issue
        await this.runTest('AbortSignal functionality with retry disabled - should work correctly', async () => {
            const sender = new Sender(`http://localhost:${this.serverPort}/slow`, {
                timeout: 5000, // Long timeout
                retry: false, // Disable retry
            })

            const abortController = new AbortController()
            const startTime = Date.now()

            // Abort the request after 500ms
            setTimeout(() => {
                console.log(`   📊 Triggering abort signal at ${Date.now() - startTime}ms (no retry)`)
                abortController.abort()
            }, 500)

            try {
                await sender.send('{"test": "abort-no-retry"}', {
                    signal: abortController.signal,
                })

                throw new Error('Request should have been aborted due to abort signal')
            } catch (error) {
                const duration = Date.now() - startTime

                console.log(`   📊 Request duration (no retry): ${duration}ms`)
                console.log(`   📊 Error type: ${error instanceof Error ? error.constructor.name : typeof error}`)
                console.log(`   📊 Error message: ${error instanceof Error ? error.message : String(error)}`)

                // With retry disabled, abort should work correctly
                if (duration > 1000) {
                    throw new Error(`Request took too long (${duration}ms) even with retry disabled`)
                }

                console.log(`   ✓ Request correctly aborted after ${duration}ms (retry disabled)`)
            }
        })
    }

    async testNormalRequestCompletion(): Promise<void> {
        await this.runTest('Normal request completion - request should complete before timeout', async () => {
            const sender = new Sender(`http://localhost:${this.serverPort}/fast`, {
                timeout: 2000, // 2 second timeout, plenty of time for fast endpoint
            })

            const startTime = Date.now()

            try {
                const response = await sender.send('{"test": "normal"}')
                const duration = Date.now() - startTime

                // Check if request completed quickly (should be much less than timeout)
                if (duration > 1000) {
                    throw new Error(`Request took too long (${duration}ms) for fast endpoint`)
                }

                // Check response structure
                if (!response.id || !response.status || !response.body) {
                    throw new Error('Response missing required fields')
                }

                if (response.status !== 200) {
                    throw new Error(`Unexpected status code: ${response.status}`)
                }

                console.log(`   ✓ Request completed successfully in ${duration}ms`)
                console.log(`   ✓ Response: ${JSON.stringify(response.body)}`)
            } catch (error) {
                throw new Error(`Normal request failed: ${error instanceof Error ? error.message : String(error)}`)
            }
        })
    }

    printSummary(): void {
        console.log(`\n${'='.repeat(60)}`)
        console.log('TEST SUMMARY')
        console.log('='.repeat(60))

        const passed = this.results.filter((r) => r.passed).length
        const failed = this.results.filter((r) => !r.passed).length

        console.log(`Total tests: ${this.results.length}`)
        console.log(`Passed: ${passed}`)
        console.log(`Failed: ${failed}`)

        if (failed > 0) {
            console.log('\nFAILED TESTS:')

            this.results.filter((r) => !r.passed).forEach((result) => {
                console.log(`- ${result.name}: ${result.error}`)
            })
        }

        console.log('\nDETAILED RESULTS:')

        this.results.forEach((result) => {
            const status = result.passed ? '✅ PASS' : '❌ FAIL'
            console.log(`${status}: ${result.name} (${result.duration}ms)`)

            if (!result.passed && result.error) {
                console.log(`  Error: ${result.error}`)
            }
        })
    }
}

async function main(): Promise<void> {
    console.log('🚀 Starting Sender Timeout & Abort Tests')
    console.log('='.repeat(60))

    const testRunner = new TestRunner()

    try {
        // Setup test server
        console.log('Setting up test server...')
        await testRunner.setupTestServer()
        console.log('Test server ready\n')

        // Run all tests
        await testRunner.testTimeoutFunctionality()
        await testRunner.testAbortSignalFunctionality()
        await testRunner.testNormalRequestCompletion()

        // Print summary
        testRunner.printSummary()
    } catch (error) {
        console.error('Test setup failed:', error)
        process.exit(1)
    } finally {
        // Cleanup
        await testRunner.teardownTestServer()
        console.log('\nTest server stopped')
    }
}

// Run tests
main().catch(console.error)
